import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// Global CSS Reset
const globalStyles = `
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    overflow-y: hidden; /* Ẩn thanh scroll dọc */
  }

  /* Ẩn thanh scroll cho tất cả các trình duyệt */
  html {
    overflow-y: hidden; /* Ẩn thanh scroll dọc cho html */
  }

  /* Ẩn thanh scroll nhưng vẫn cho phép scroll (tùy chọn) */
  /*
  body::-webkit-scrollbar {
    display: none; /* Ẩn thanh scroll trên Chrome, Safari, Edge */
  }

  body {
    -ms-overflow-style: none; /* Ẩn thanh scroll trên IE và Edge */
    scrollbar-width: none; /* Ẩn thanh scroll trên Firefox */
  }
  */

  a {
    text-decoration: none;
    color: inherit;
  }

  ul {
    list-style: none;
  }

  button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
  }

  img {
    max-width: 100%;
    height: auto;
  }
`;

// Inject global styles
const styleSheet = document.createElement('style');
styleSheet.textContent = globalStyles;
document.head.appendChild(styleSheet);


const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);


